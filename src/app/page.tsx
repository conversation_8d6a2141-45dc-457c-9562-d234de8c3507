"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import HeroSection from "@/components/home/<USER>";
import SearchSection from "@/components/home/<USER>";
import DestinationsSection from "@/components/home/<USER>";
import PackagesSection from "@/components/home/<USER>";
import CarCharterSection from "@/components/home/<USER>";
import WhyChooseUsSection from "@/components/home/<USER>";
import ContactSection from "@/components/home/<USER>";
import BlogSection from "@/components/home/<USER>";
import InteractiveMapSection from "@/components/home/<USER>";
import AnimatedSection from "@/components/home/<USER>";

type SectionWrapperProps = {
    children: React.ReactNode;
    className?: string;
};

export const SectionWrapper = ({
    children,
    className,
}: SectionWrapperProps) => {
    return <div className={className}>{children}</div>;
};

export default function Home(): React.JSX.Element {
    const pageSections = [
        SearchSection,
        CarCharterSection,
        DestinationsSection,
        InteractiveMapSection,
        PackagesSection,
        BlogSection,
        WhyChooseUsSection,
        ContactSection,
    ];

    return (
        <>
            <HeroSection />
            <React.Suspense>
                {pageSections.map((SectionComponent, index) => (
                    <SectionWrapper
                        key={index}
                        className={cn(
                            index % 2 === 0 ? "bg-secondary" : "bg-background",
                        )}
                    >
                        <AnimatedSection>
                            <SectionComponent />
                        </AnimatedSection>
                    </SectionWrapper>
                ))}
            </React.Suspense>
        </>
    );
}
